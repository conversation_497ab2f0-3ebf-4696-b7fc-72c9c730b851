<template>
    <BasicModal v-bind="$attrs" @register="registerModal" title="人员上岗能力确认记录" okText="关闭" @ok="handleSubmit"
        :width="1400">
        <div class="table-container">
            <div class="table-header">
                <div class="table-actions">
                    <button class="action-btn" @click="printTable">打印</button>
                </div>
            </div>
            <table :id="printId" border="1" cellspacing="0" cellpadding="5"
                style="width: 100%; border-collapse: collapse">
                <thead>
                    <tr>
                        <th style="text-align: center" colspan="6">人员上岗能力确认记录</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th style="width: 3rem;">姓名：</th>
                        <td style="width: 4rem;"></td>
                        <th style="width: 3rem;">性别：</th>
                        <td style="width: 3rem;"></td>
                        <th style="width: 3rem;">年龄：</th>
                        <td style="width: 3rem;"></td>
                    </tr>
                    <tr>
                        <th rowspan="4">拟授权范围：</th>
                        <th>岗位：</th>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <th>检测项目：</th>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <th>操作仪器：</th>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <th>其他授权：</th>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <th rowspan="5">确认依据：</th>
                        <th>学历/专业：</th>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <th>工作经历：</th>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <th>技术职称/资质证书：</th>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <th>培训/考核：</th>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <th>其他方面：</th>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <th>确认结论：</th>
                        <td colspan="5">
                            <br>
                            <br>
                            <br>
                            <a-row>
                                <a-col :span="12">技术负责人：</a-col>
                                <a-col :span="12">日期:</a-col>
                            </a-row>

                        </td>
                    </tr>
                    <tr>
                        <th>实验室审批意见：</th>
                        <td colspan="5">
                            <br>
                            <br>
                            <br>
                            <a-row>
                                <a-col :span="12">实验室经理：</a-col>
                                <a-col :span="12">日期:</a-col>
                            </a-row>

                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    printId.value = buildUUID().toString();
});

// 打印表格
function printTable() {
    printJS({
        type: 'html',
        printable: printId.value,
        scanStyles: false,
    });
}

async function handleSubmit() {
    closeModal();
}
</script>

<style scoped>
.table-container {
    padding: 20px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-title {
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 5px 10px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.action-btn:hover {
    background-color: #40a9ff;
}

.action-btn:disabled {
    background-color: #d9d9d9;
    cursor: not-allowed;
}

table {
    border: 1px solid #ccc;
    width: 100%;
}

th,
td {
    border: 1px solid #ccc;
    text-align: center;
    padding: 8px;
    height: 40px;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}
</style>
