<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <a-form 
        ref="formRef" 
        :model="orderMainModel" 
        @submit="handleSubmit" 
        :label-col="labelCol" 
        :wrapper-col="wrapperCol" 
        :rules="validatorRules">
        
      <a-row class="form-row" :gutter="8">
        <a-col :span="20">
          <a-form-item label="计划内容" name="content">
            <a-input v-model:value="orderMainModel.content" placeholder="请输入计划内容" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="计划起止时间" name="duration">
            <a-range-picker v-model:value="orderMainModel.duration" @change="changeDuration"/>
            <!-- <a-input v-model:value="orderMainModel.typeName" placeholder="请输入名称" /> -->
          </a-form-item>
        </a-col>
        <a-col :span="20">
            <a-form-item label="文件操作">
                <a-button type="dashed" block @click="addSight">
                    <PlusOutlined />
                    增加需上传文件
                </a-button>
            </a-form-item>
        </a-col>
        <a-col :span="24"
            v-for="(sight, index) in orderMainModel.recordFile"
            :key="`z`+index">
            <a-row>
                <a-col :span="16">
                    <a-form-item
                        :name="['recordFile', index, 'requireVal']"
                        :label="`文件` + (index+1) + `要求`"
                        :rules="{
                            required: true,
                            message: '必填',
                        }"
                    >
                        
                        <a-input v-model:value="sight.requireVal" />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-upload
                    v-model:file-list="sight.fileList"
                    name="file"
                    accept=".pdf"
                    :data="{biz:'perosnManagement'}"
                    :action="uploadUrl"
                    :headers="headers"
                    :before-upload="beforeUpload"
                    @change="handleChange"
                    @preview="onFilePreview"
                  >
                    <a-button>
                      <upload-outlined></upload-outlined>
                      上传
                    </a-button>
                  </a-upload>

                  <!-- <JUpload v-model:value="sight.fileList" bizPath="perosnManagement" fileType="pdf"></JUpload> -->
                </a-col>
            </a-row>
                
                
            
        </a-col>

        <!-- <a-col :span="20">
            <a-form-item label="文件操作">
                <a-button type="dashed" block @click="addSight">
                    <PlusOutlined />
                    增加需上传文件
                </a-button>
            </a-form-item>
        </a-col> -->

        
        <a-col :span="20" v-show="false">
          <a-form-item label="指派人员" name="orderUserCode">
            <a-input disabled v-model:value="orderMainModel.orderUserCode" placeholder="请输入指派人员" />
          </a-form-item>
        </a-col>
        <a-col :span="20">
          <a-form-item label="指派人员" name="orderUserName">
            <a-input :class="{fontColor:true}" disabled v-model:value="orderMainModel.orderUserName" placeholder="请输入指派人员" />
          </a-form-item>
        </a-col>
        <a-col :span="2">
            <a-button preIcon="ant-design:user-switch-outlined" @click="addPerson">添加人员</a-button>
        </a-col>
        <!-- <a-col :span="20">
          <a-form-item label="类型" name="type">
            <a-select placeholder="请选择类型" v-model:value="orderMainModel.type">
              <a-select-option value="1">单人间</a-select-option>
              <a-select-option value="2">一室一厅</a-select-option>
              <a-select-option value="3">两室一厅</a-select-option>
              <a-select-option value="4">三室一厅</a-select-option>
              <a-select-option value="6">四人间</a-select-option>
              <a-select-option value="7">八人间</a-select-option>
              <a-select-option value="8">十人间</a-select-option>
              <a-select-option value="9">夫妻房(普工)</a-select-option>
              <a-select-option value="10">夫妻房(管理)</a-select-option>
            </a-select>
          </a-form-item>
        </a-col> -->
      </a-row>
    </a-form>
    <ChoosePersonModal @register="cpModal" @success="handleCpReturn"></ChoosePersonModal>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form/index';
import { getToken } from '/@/utils/auth';
import { uploadUrl } from '/@/api/common/api';
import { formSchema } from '../capabilityConfirm.data';
import { saveOrUpdate } from '../capabilityConfirm.api';
import ChoosePersonModal from '/@/views/environment/taskAllocation/modules/ChoosePersonModal.vue'
import { MinusCircleOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { message, Upload } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref<FormInstance>();
const isUpdate = ref(true);
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 5 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});
const orderMainModel = reactive({
  id: null,
  content: null,
  duration: null,
  startDate: null,
  endDate: null,
  orderUserCode: '',
  orderUserName: '',
  recordFile: [],
  // peopleNumber: '',
});
const headers = reactive({
  'X-Access-Token': getToken(),
});
let site = ref(0);
let fileList = ref([]);
const validatorRules = {
  content: [
    { required: true, message: '名称必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' },
  ],
  duration: [{ required: true, message: '必选！' }],
  orderUserName: [{ required: true, message: '必选！' }],
  type: [{ required: true, message: '必选！' }],
};
//表单配置
// const [registerForm, {resetFields, setFieldsValue, validate}] = useForm({
//     labelWidth: 150,
//     schemas: formSchema,
//     showActionButtonGroup: false,
// });
//表单赋值
const [cpModal, {openModal: choosePModal}] = useModal();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  // await resetFields();
  // setTimeout(()=>{
  formRef.value.resetFields();
  // },300)
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {
    //表单赋值
    // await setFieldsValue({
    //     ...data.record,
    // });
    Object.assign(orderMainModel, data.record);
  } else {
    addSight();
  }
  console.log('🚀 ~ file:  ~ orderMainModel:', orderMainModel);
});
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
//表单提交事件
function handleSubmit(v) {
  formRef.value
    .validate()
    .then(async () => {
      try {
        // let values = await validate();
        setModalProps({ confirmLoading: true });
        //提交表单
        await saveOrUpdate(orderMainModel, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, orderMainModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });
}
function reset() {
    orderMainModel.id = null;
    orderMainModel.content = null;
    orderMainModel.duration = null;
    orderMainModel.startDate = null;
    orderMainModel.endDate = null;
    orderMainModel.orderUserCode = '';
    orderMainModel.orderUserName = '';
    orderMainModel.recordFile = [];
    site.value = 0;
  // orderMainModel.peopleNumber = '';
}
function changeDuration(dates: [dayjs, dayjs] | [string, string], dateStrings: [string, string]) {
    console.log("🚀 ~ file: DormitoryAreaModal.vue:109 ~ onChange ~ value, option:", dates, dateStrings)
    orderMainModel.startDate = dateStrings[0];
    orderMainModel.endDate = dateStrings[1];
}

function addPerson() {
    choosePModal(true, {
        isUpdate: false,
        showFooter: true,
    });
}

function handleCpReturn(source) {
  console.log("🚀 ~ file: PlanDevelopmentModal.vue:163 ~ handleCpReturn ~ source:", source)
    
    if(source.length != 0) {
        orderMainModel.orderUserCode = '';
        orderMainModel.orderUserName = '';

        for(let i = 0; i < source.length; i++) {
            orderMainModel.orderUserCode += source[i].username;
            orderMainModel.orderUserName += source[i].realname;

            if(i + 1 != source.length) {
                orderMainModel.orderUserCode += ',';
                orderMainModel.orderUserName += ',';
            }
        }
    }
    
}

function addSight() {
    let curId = 'z'+site.value;
    site.value++;
    orderMainModel.recordFile.push({
        id:curId,
        requireVal:'',
        fileList:[]
    })
}
function removeSight(sight) {
console.log("🚀 ~ file: PlanDevelopmentModal.vue:222 ~ removeSight ~ sight:", sight)
    orderMainModel.recordFile = orderMainModel.recordFile.filter((item) => item.id !== sight.id);
}

const handleChange = (info: UploadChangeParam) => {
  console.log("🚀 ~ file: PlanProcessModal.vue:271 ~ handleChange ~ info:", info)
  if (info.file.status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  if (info.file.status === 'done') {
    message.success(`${info.file.name} file uploaded successfully`);
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} file upload failed.`);
  }
};

// 预览文件、图片
function onFilePreview(file) {
  // if (isImageMode.value) {
  //   createImgPreview({ imageList: [file.url], maskClosable: true });
  // } else {
    
  // }

  window.open(file.url);
}
const beforeUpload: UploadProps['beforeUpload'] = file => {
  console.log("🚀 ~ file: PlanProcessModal.vue:302 ~ file:", file)
  const isPDf = file.type === 'application/pdf';
  if (!isPDf) {
    message.error(`${file.name} 不是一个pdf文件`);
  }
  console.log('',orderMainModel.recordFile)
  return isPDf || Upload.LIST_IGNORE;
};
</script>

<style lang="less" scoped>
.fontColor {
    color: black;
}
</style>