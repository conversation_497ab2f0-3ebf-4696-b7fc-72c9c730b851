const panel = {"panels":[{"index":0,"name":1,"height":297,"width":210,"paperHeader":49.5,"paperFooter":780,"printElements":[{"options":{"left":0,"top":0,"height":48,"width":594,"title":"技术人员岗位授权一览","right":592.5,"bottom":23.49609375,"vCenter":295.5,"hCenter":12.24609375,"coordinateSync":false,"widthHeightSync":false,"fontFamily":"Microsoft YaHei","fontSize":24,"fontWeight":"800","letterSpacing":3,"textAlign":"center","textContentVerticalAlign":"middle","qrCodeLevel":0},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":2.5,"top":60,"height":280.5,"width":588,"right":588,"bottom":339.4921875,"vCenter":294,"hCenter":199.2421875,"tableFooterRepeat":"page","coordinateSync":false,"widthHeightSync":false,"tableHeaderRepeat":"page","tableHeaderBorder":"noBorder","tableHeaderBackground":"#ffffff","tableHeaderFontSize":9,"columns":[[{"width":36.671254559383854,"title":"编号","field":"id","checked":true,"columnId":"id","fixed":false,"rowspan":1,"colspan":1},{"width":48.16248128830558,"title":"姓名","field":"name","checked":true,"columnId":"name","fixed":false,"rowspan":1,"colspan":1},{"width":68.0394667519991,"title":"岗位","field":"gw","checked":true,"columnId":"gw","fixed":false,"rowspan":1,"colspan":1},{"width":88.71961265361855,"title":"检测项目","field":"jcxm","checked":true,"columnId":"jcxm","fixed":false,"rowspan":1,"colspan":1},{"width":71.59763313609467,"title":"操作仪器","field":"czyq","checked":true,"columnId":"czyq","fixed":false,"rowspan":1,"colspan":1},{"width":51.26221070690802,"title":"报告审核","field":"bgsh","checked":true,"columnId":"bgsh","fixed":false,"rowspan":1,"colspan":1},{"width":100.93195628830559,"title":"授权签发报告","checked":true,"fixed":false,"rowspan":1,"colspan":1},{"width":84.61538461538461,"title":"其他授权","checked":true,"fixed":false,"rowspan":1,"colspan":1}]]},"printElementType":{"title":"空白表格","type":"table","editable":true,"columnDisplayEditable":true,"columnDisplayIndexEditable":true,"columnTitleEditable":true,"columnResizable":true,"columnAlignEditable":true,"isEnableEditField":true,"isEnableContextMenu":true,"isEnableInsertRow":true,"isEnableDeleteRow":true,"isEnableInsertColumn":true,"isEnableDeleteColumn":true,"isEnableMergeCell":true}}],"paperNumberLeft":565.5,"paperNumberTop":819,"paperNumberContinue":true,"watermarkOptions":{"content":"vue-plugin-hiprint","rotate":25,"timestamp":false,"format":"YYYY-MM-DD HH:mm","fillStyle":"rgba(184, 184, 184, 0.3)","fontSize":"14px","width":200,"height":200},"panelLayoutOptions":{"layoutType":"column","layoutRowGap":0,"layoutColumnGap":0}}]}
export default panel;
