import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  // {
  //   title: '序号',
  //   dataIndex: 'content'
  // },
  {
    title: '人员名称',
    dataIndex: 'userName'
  },
  {
    title: '计划完成培训考核数量',
    dataIndex: 'planFinishCount'
  },
  {
    title: '实际完成培训考核数量',
    dataIndex: 'realFinishCount'
  },
  {
    title: '完成率',
    dataIndex: 'finishRate'
  },
  // {
  //   title: '状态',
  //   dataIndex: 'status',
  //   customRender: ({ text }) => {
  //     if (text == '0') {
  //       return '正常';
  //     } else if (text == '1') {
  //       return '失效';
  //     } else {
  //       return text;
  //     }
  //   },
  // },
  // {
  //   title: '创建人',
  //   dataIndex: 'creator_dictText'
  // },
  // {
  //   title: '创建时间',
  //   dataIndex: 'createTime'
  // },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '用户编码',
    field: 'userCode',
    component: 'Input'
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: '车间',
    field: 'workshopName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '洁净级别',
    field: 'cleanLevel',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '检测区域',
    field: 'areaName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
];
